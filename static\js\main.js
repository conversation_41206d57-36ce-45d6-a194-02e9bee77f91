(function () {
	"use strict";

	var treeviewMenu = $('.app-menu');

	// Toggle Sidebar
	$('[data-toggle="sidebar"]').click(function(event) {
		event.preventDefault();
		$('.app').toggleClass('sidenav-toggled');
	});
	// Enhanced sidebar treeview toggle with smooth animations
	$("[data-toggle='treeview']").click(function(event) {
		event.preventDefault();
		
		const $clickedItem = $(this);
		const $parentLi = $clickedItem.parent();
		const $chevronIcon = $clickedItem.find('.toggler-plus-icon');
		const $treeviewMenu = $parentLi.find('.treeview-menu');
		
		// Add loading state
		$chevronIcon.addClass('loading');
		
		// Close other expanded menus with animation
		if(!$parentLi.hasClass('is-expanded')) {
			treeviewMenu.find("[data-toggle='treeview']").parent().each(function() {
				const $otherItem = $(this);
				if($otherItem.hasClass('is-expanded') && !$otherItem.is($parentLi)) {
					$otherItem.removeClass('is-expanded');
					// Reset chevron rotation
					$otherItem.find('.toggler-plus-icon').css('transform', 'rotate(0deg)');
				}
			});
		}
		
		// Toggle current menu with enhanced animation
		setTimeout(() => {
			$parentLi.toggleClass('is-expanded');
			
			// Handle chevron rotation animation
			if($parentLi.hasClass('is-expanded')) {
				$chevronIcon.css({
					'transform': 'rotate(90deg)',
					'transition': 'transform 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
				});
				
				// Animate menu items with stagger effect
				$treeviewMenu.find('li').each(function(index) {
					$(this).css({
						'animation-delay': `${index * 0.05}s`,
						'animation-name': 'slideInFromLeft'
					});
				});
			} else {
				$chevronIcon.css({
					'transform': 'rotate(0deg)',
					'transition': 'transform 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
				});
			}
			
			// Remove loading state
			$chevronIcon.removeClass('loading');
		}, 50);
		
		// Add haptic feedback for supported devices
		if (navigator.vibrate) {
			navigator.vibrate(50);
		}
	});

	// Set initial active toggle
	$("[data-toggle='treeview.'].is-expanded").parent().toggleClass('is-expanded');

	//Activate bootstrip tooltips
	$("[data-toggle='tooltip']").tooltip();

// Store install prompt event
let deferredPrompt = null;

// Get install button reference
const installButton = document.getElementById('install');

// Hide install button by default
if (installButton) {
    installButton.style.display = 'none';
}

// Listen for beforeinstallprompt
window.addEventListener('beforeinstallprompt', (event) => {
    // Store event for later use
    deferredPrompt = event;

    // Show install button/banner
    if (installButton) {
        installButton.style.display = 'block';
    }
});

// Handle install button click
if (installButton) {
    installButton.addEventListener('click', async () => {
        if (!deferredPrompt) {
            // App already installed or not available
            Swal.fire({
                title: "Installation impossible",
                text: "L'application semble être déjà installée sur votre appareil. Si le problème persiste, prière contacter le : +225 07 59 95 14 53 (Appel ou Whatsapp)",
                icon: 'info',
            });
            return;
        }

        // Show install prompt
        const result = await deferredPrompt.prompt();

        // Wait for user response
        const outcome = await deferredPrompt.userChoice;

        if (outcome.outcome === 'accepted') {
            // Clear the deferredPrompt
            deferredPrompt = null;
            // Hide install button
            installButton.style.display = 'none';

            Swal.fire({
                title: "Installation terminée",
                text: "L'application EcolePro a été installée avec succès! Pour les téléphones mobile, il peut prendre jusqu'à une minute avant d'être visible dans le Menu Applis.",
                icon: 'success',
            });
        }
    });
}

// Handle installed event
window.addEventListener('appinstalled', () => {
    // Clear prompt
    deferredPrompt = null;
    // Hide button
    if (installButton) {
        installButton.style.display = 'none';
    }
});


	// Bottom Navigation
	var links = document.querySelectorAll('.bottom-nav-link');
	links.forEach(link => {
	link.addEventListener('click', (ev) => {
		links.forEach(li => {
		li.classList.remove('active');
		})
		ev.target.closest('a').classList.add('active');
	})
	}
	);

	// ================================================
	// ENHANCED NAVIGATION ANIMATIONS
	// ================================================
	
	// Initialize smooth scrolling for treeview navigation
	function initSmoothNavigation() {
		// Add intersection observer for viewport animations
		if ('IntersectionObserver' in window) {
			const observer = new IntersectionObserver((entries) => {
				entries.forEach(entry => {
					if (entry.isIntersecting) {
						entry.target.style.opacity = '1';
						entry.target.style.transform = 'translateX(0)';
					}
				});
			}, {
				threshold: 0.1
			});
			
			// Observe all treeview items
			document.querySelectorAll('.treeview-item').forEach(item => {
				item.style.opacity = '0';
				item.style.transform = 'translateX(-20px)';
				item.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
				observer.observe(item);
			});
		}
		
		// Enhanced hover effects
		$('.app-menu__item').hover(
			function() {
				$(this).find('.toggler-plus-icon').css({
					'transform': $(this).parent().hasClass('is-expanded') 
						? 'rotate(90deg) scale(1.1)' 
						: 'rotate(0deg) scale(1.1)',
					'transition': 'transform 0.2s ease'
				});
			},
			function() {
				$(this).find('.toggler-plus-icon').css({
					'transform': $(this).parent().hasClass('is-expanded') 
						? 'rotate(90deg) scale(1)' 
						: 'rotate(0deg) scale(1)',
					'transition': 'transform 0.2s ease'
				});
			}
		);
	}
	
	// Function to animate menu collapse/expand
	function animateTreeviewMenu($menu, isExpanding) {
		if (isExpanding) {
			$menu.css({
				'max-height': '0px',
				'opacity': '0',
				'overflow': 'hidden'
			}).animate({
				'max-height': '800px',
				'opacity': '1'
			}, {
				duration: 400,
				easing: 'swing',
				complete: function() {
					$(this).css('overflow', 'visible');
				}
			});
		} else {
			$menu.animate({
				'max-height': '0px',
				'opacity': '0'
			}, {
				duration: 300,
				easing: 'swing',
				complete: function() {
					$(this).css('overflow', 'hidden');
				}
			});
		}
	}
	
	// Handle keyboard navigation for accessibility
	function initKeyboardNavigation() {
		$(document).keydown(function(e) {
			const $focused = $(':focus');
			
			if ($focused.hasClass('app-menu__item')) {
				switch(e.keyCode) {
					case 13: // Enter key
					case 32: // Space key
						e.preventDefault();
						$focused.click();
						break;
					case 37: // Left arrow - collapse
						if ($focused.parent().hasClass('is-expanded')) {
							$focused.click();
						}
						break;
					case 39: // Right arrow - expand
						if (!$focused.parent().hasClass('is-expanded')) {
							$focused.click();
						}
						break;
				}
			}
		});
	}
	
	// Debounce function for performance
	function debounce(func, wait) {
		let timeout;
		return function executedFunction(...args) {
			const later = () => {
				clearTimeout(timeout);
				func(...args);
			};
			clearTimeout(timeout);
			timeout = setTimeout(later, wait);
		};
	}
	
	// Smooth scroll to active menu item
	function scrollToActiveMenuItem() {
		const $activeItem = $('.app-menu .app-menu__item.active').first();
		if ($activeItem.length) {
			const sidebar = $('.app-sidebar')[0];
			const itemTop = $activeItem.offset().top;
			const sidebarTop = $(sidebar).offset().top;
			const targetScrollTop = itemTop - sidebarTop - 100;
			
			$(sidebar).animate({
				scrollTop: targetScrollTop
			}, 300);
		}
	}
	
	// Initialize all navigation enhancements
	function initNavigationEnhancements() {
		initSmoothNavigation();
		initKeyboardNavigation();
		
		// Auto-expand parent menu of active item
		$('.treeview-item.active').each(function() {
			const $parentTreeview = $(this).closest('.treeview');
			if ($parentTreeview.length && !$parentTreeview.hasClass('is-expanded')) {
				$parentTreeview.addClass('is-expanded');
				$parentTreeview.find('.toggler-plus-icon').css('transform', 'rotate(90deg)');
			}
		});
		
		// Scroll to active item after initialization
		setTimeout(scrollToActiveMenuItem, 500);
	}
	
	// Call initialization when document is ready
	$(document).ready(function() {
		initNavigationEnhancements();
	});

	// Datatable initialisation
function loadDatatable() {
  // Destroy existing DataTable if it exists
  if ($.fn.DataTable.isDataTable('#datatable')) {
    $('#datatable').DataTable().destroy();
  }

  // Initialize new DataTable
  $.fn.dataTable.ext.errMode = 'none';
  $('#datatable').DataTable({
    // ... your existing options ...
    lengthMenu: [
      [10, 25, 50, 100, 200, 300],
      ['10', '25', '50', '100', '200', '300']
    ],
    buttons: [
      'colvis', 'pageLength'
    ],
    error: function(xhr, error, thrown) {
      console.log('Datatables', error);
    },
    drawCallback: function() {
      $('.lazy:lt(4)').each(function() {
        $(this).attr('src', $(this).data('original')).removeClass('lazy');
      });
      $('img.lazy').lazyload({
        load: function() { $(this).removeClass("lazyload"); }
      });
      feather.replace();
      htmx.process(document.body);
    }
  });
}

	$(document).ready(function() {
		feather.replace();
		Waves.attach('.btn, li, .ripple', ['waves-light']);
		Waves.init();
		$(document).ready(function(){
			$(".checkbox-table").simpleCheckboxTable();
		  });
		$.extend( $.fn.dataTable.defaults, {
		  language: {
			  "sProcessing": "Traitement en cours...",
			  "sSearch": "Rechercher&nbsp;:",
			  "sLengthMenu": "Afficher _MENU_ &eacute;l&eacute;ments",
			  "sInfo": "Affichage de l'&eacute;l&eacute;ment _START_ &agrave; _END_ sur _TOTAL_ &eacute;l&eacute;ments",
			  "sInfoEmpty": "Affichage de l'&eacute;l&eacute;ment 0 &agrave; 0 sur 0 &eacute;l&eacute;ments",
			  "sInfoFiltered": "(filtr&eacute; de _MAX_ &eacute;l&eacute;ments au total)",
			  "sInfoPostFix": "",
			  "sLoadingRecords": "Chargement en cours...",
			  "sZeroRecords": "Aucun &eacute;l&eacute;ment &agrave; afficher",
			  "sEmptyTable": "Aucune donn&eacute;e disponible dans le tableau",
			  "oPaginate": {
				  "sFirst": "Premier",
				  "sPrevious": "Pr&eacute;c&eacute;dent",
				  "sNext": "Suivant",
				  "sLast": "Dernier"
			  },
			  "oAria": {
				  "sSortAscending": ": activer pour trier la colonne par ordre croissant",
				  "sSortDescending": ": activer pour trier la colonne par ordre d&eacute;croissant"
			  }
		  }
	  });

	  loadDatatable();
	})
	// Htmx
	document.body.addEventListener('htmx:configRequest', (event) => {
		event.detail.headers['X-CSRFToken'] = '{{ csrf_token }}';
	});

	document.addEventListener('htmx:load', function(evt) {
		// Small delay to ensure DOM is ready
		setTimeout(() => {
			if (!$.fn.DataTable.isDataTable('#datatable')) {
				loadDatatable();
			}

			// Initialize Feather icons after HTMX loads
			if (typeof feather !== 'undefined') {
				feather.replace();
			}
		}, 100);
	});

	  htmx.on("htmx:afterSwap", (e) => {
		// Response targeting #dialog => show the modal
		if (document.body.classList.contains('pace-running')) {
			document.body.classList.remove('pace-running');
		};

		if (e.detail.target.id == "dialog") {
		  $("#modal").modal("show")
		} else if(e.detail.target.id == "dialog-xl") {
		  $("#modal-xl").modal("show")
		}

		let photoInput = document.getElementById('id_3-photo');
		if (photoInput !== null) {
			reduceImageSize('id_3-photo', 600, 600, true);
		}
		let photoInput2 = document.getElementById('id_2-photo');
		if (photoInput2 !== null) {
			reduceImageSize('id_2-photo', 600, 600, true);
		}
		let photoInput3 = document.getElementById('id_photo');
		if (photoInput3 !== null) {
			reduceImageSize('id_photo', 600, 600, true);
		}
	  });

	  htmx.on("htmx:beforeSwap", (e) => {
		// Empty response targeting #dialog => hide the modal
		if (e.detail.target.id !== 'dialog' || e.detail.target.id !== 'dialog-xl') {
			var status_code = e.detail.xhr.status;
			if (status_code === 204) {
				Swal.fire({
					title: 'Fait',
					text: 'Action éffectuée avec succès',
					icon: 'success',
				});
			} else if (status_code === 206) {
				var jsonResponse = JSON.parse(e.detail.xhr.responseText);
				$("#modal-xl").modal("hide");
				Swal.fire({
					title: "Enregistré!",
					html: "Enregistré avec succès. Voulez-vous imprimer un reçu de paiement? Vous pouvez choisir entre deux modèles: <br> <strong> * Modèle 1 (par Rubrique : inscription, scolarité, etc)</strong> <br><strong> * Modèle 2 (qui affiche la liste de tous les paiements)</strong>.",
					icon: "success",
					showCancelButton: true,
					showDenyButton: true,
					confirmButtonColor: "#198754",
					cancelButtonColor: "#d33",
					denyButtonColor: "#3085d6",
					confirmButtonText: "Oui, Modèle 1",
					cancelButtonText: 'Non',
					denyButtonText: 'Modèle 2',
				  }).then((result) => {
					if (result.isConfirmed) {
					  window.open(jsonResponse['receipt_url'] + '?template=2&copies=1', '_blank');
					} else if (result.isDenied) {
						window.open(jsonResponse['receipt_url'] + '?template=1&copies=1', '_blank');
					} else {
						Swal.fire({
							title: "Modifications enregistrées!",
							icon: "success",
							toast: true,
							showConfirmButton: false,
							timer: 2000,
							position: 'bottom-end',
							showCloseButton: true,
						  })
					}
				  });
			} else if (status_code === 201) {
				$("#modal").modal("hide");
			};

		}
		if ((e.detail.target.id == "dialog" || e.detail.target.id == 'dialog-xl') && !e.detail.xhr.response) {
			if(e.detail.target.id == 'dialog') {
				$("#modal").modal("hide");
			} else if (e.detail.target.id == 'dialog-xl') {
				$("#modal-xl").modal("hide");
			}
			e.detail.shouldSwap = false;
			if (e.detail.xhr.status === 204) {
				Swal.fire({
					title: 'Fait',
					text: 'Action éffectuée avec succès',
					icon: 'success',
				});
			};
			if (e.detail.xhr.status === 205) {
				Swal.fire({
					title: 'Compte en cours de création',
					text: 'Votre compte est en cours de création. Vous recevrez un message de ECOLEPRO-CI contenant les informations de connexion dans peu de temps. Pour toute assistance technique: +225 07 59 95 14 53 (Whatsapp ou Appel)',
					icon: 'success',
				});
			};
			if (e.detail.xhr.status === 403) {
				Swal.fire({
					title: 'Autorisation réfusée',
					text: "Vous n'êtes pas autorisé à voir cette page ou à éffectuer cette action.",
					icon: 'error',
				});
			};
		}
	  })

	  // Remove dialog content after hiding
	  $("#modal").on("hidden.bs.modal", () => {
		$("#dialog").empty()
	  })
	  $("#modal-xl").on("hidden.bs.modal", () => {
		$("#dialog-xl").empty()
	  })

	if (typeof(feather) != "undefined") feather.replace();

	function reduceImageSize(inputId, maxWidth, maxHeight, isPhoto) {
		const fileInput = document.getElementById(inputId);
		fileInput.addEventListener('change', (event) => {
		  const file = event.target.files[0];
		  const reader = new FileReader();

		  const fileSize = file.size / 1024;
		  if ((isPhoto && fileSize <= 50) || (!isPhoto && fileSize <= 100)) {
			return;
		  } else if (isPhoto && fileSize <= 400) {
			maxWidth = 300;
			maxHeight = 300;
		  }
		  console.log('Initial size', fileSize);
		  document.body.classList.add('pace-running');

		  reader.onload = (readerEvent) => {
			const image = new Image();

			image.onload = () => {
			  const canvas = document.createElement('canvas');
			  let width = image.width;
			  let height = image.height;

			  if (width > height && width > maxWidth) {
				height *= maxWidth / width;
				width = maxWidth;
			  } else if (height > maxWidth) {
				width *= maxHeight / height;
				height = maxHeight;
			  }

			  canvas.width = width;
			  canvas.height = height;

			  const context = canvas.getContext('2d');
			  context.drawImage(image, 0, 0, width, height);

			  const dataUrl = canvas.toDataURL(file.type);
			  const newFile = dataURLtoFile(dataUrl, file.name);

			  // Create a new FileList object with the resized file
			  const newFileList = createFileList(newFile);

			  // Replace the entire FileList in the file input
			  fileInput.files = newFileList;

			// Replace the initial image with the resized version
			// fileInput.files[0] = newFile;
			  fileInput.files = newFileList;
			  console.log('New size:', newFile.size / 1024)
			  document.body.classList.remove('pace-running');
			};

			image.src = readerEvent.target.result;
		  };

		  reader.readAsDataURL(file);
		});
	  }

	  // Helper function to convert data URL to a File object
	  function dataURLtoFile(dataUrl, fileName) {
		const arr = dataUrl.split(',');
		const mime = arr[0].match(/:(.*?);/)[1];
		const bstr = atob(arr[1]);
		let n = bstr.length;
		const u8arr = new Uint8Array(n);

		while (n--) {
		  u8arr[n] = bstr.charCodeAt(n);
		}

		return new File([u8arr], fileName, { type: mime });
	  }

	// Helper function to create a new FileList object
	function createFileList(...files) {
		const dataTransfer = new DataTransfer();
		for (const file of files) {
		dataTransfer.items.add(file);
		}
		return dataTransfer.files;
	}

	// Close nav on nav-item click
	document.body.querySelectorAll('.close-on-click').forEach(element => {
		element.addEventListener('click', (e) =>{
		  let body = document.body
		  if(body !== null && body.classList.contains('sidenav-toggled') && (window.innerWidth < 768)) {
			body.classList.remove('sidenav-toggled')
		  }
		})
	});

	// Login animation
	var words = document.getElementsByClassName('animation-word');
	var wordArray = [];
	var currentWord = 0;

	var word = words[currentWord];
	if (typeof(word) !== 'undefined') word.style.opacity = 1;
	for (var i = 0; i < words.length; i++) {
	splitLetters(words[i]);
	}

	function changeWord() {
	var cw = wordArray[currentWord];

	if (typeof(cw) !== 'undefined') {
		var nw = currentWord == words.length-1 ? wordArray[0] : wordArray[currentWord+1];
		for (var i = 0; i < cw.length; i++) {
			animateLetterOut(cw, i);
		}

		for (var i = 0; i < nw.length; i++) {
			nw[i].className = 'letter behind';
			nw[0].parentElement.style.opacity = 1;
			animateLetterIn(nw, i);
		}

		currentWord = (currentWord == wordArray.length-1) ? 0 : currentWord+1;
		}
	}


	function animateLetterOut(cw, i) {
	setTimeout(function() {
			cw[i].className = 'letter out';
	}, i*80);
	}

	function animateLetterIn(nw, i) {
	setTimeout(function() {
			nw[i].className = 'letter in';
	}, 340+(i*80));
	}

	function splitLetters(word) {
	var content = word.innerHTML;
	word.innerHTML = '';
	var letters = [];
	for (var i = 0; i < content.length; i++) {
		var letter = document.createElement('span');
		letter.className = 'letter';
		letter.innerHTML = content.charAt(i);
		word.appendChild(letter);
		letters.push(letter);
	}

	wordArray.push(letters);
	}

	changeWord();
	setInterval(changeWord, 4000);

})();
